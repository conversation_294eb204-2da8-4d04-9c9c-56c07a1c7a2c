"""
Shared utilities for MCP integration.
Contains common functions used across multiple MCP modules to eliminate duplication.
"""
from __future__ import annotations
from typing import Dict, List, Any, Optional
from ..logger_config import logger, log_backend_activity


def _log_mcp_activity(backend_name: Optional[str], message: str, details: Dict[str, Any], agent_id: Optional[str] = None) -> None:
    """Log MCP activity with backend context if available."""
    if backend_name:
        log_backend_activity(backend_name, f"MCP: {message}", details, agent_id=agent_id)
    else:
        logger.info(f"MCP {message}", extra=details)


def normalize_mcp_servers(servers: Any, backend_name: Optional[str] = None) -> List[Dict[str, Any]]:
    """Validate and normalize mcp_servers into a list of dicts.

    Args:
        servers: MCP servers configuration (list, dict, or None)
        backend_name: Optional backend name for logging context

    Returns:
        Normalized list of server dictionaries
        
    Note:
        This function uses graceful error handling - it logs errors and continues
        processing rather than raising exceptions. Invalid servers are skipped.
    """
    if not servers:
        return []

    if isinstance(servers, dict):
        servers = [servers]

    if not isinstance(servers, list):
        _log_mcp_activity(backend_name, "invalid mcp_servers type", {"type": type(servers).__name__, "expected": "list or dict"})
        return []

    normalized = []
    for i, server in enumerate(servers):
        if not isinstance(server, dict):
            _log_mcp_activity(backend_name, "skipping invalid server", {"index": i, "server": str(server)})
            continue

        if "type" not in server:
            _log_mcp_activity(backend_name, "server missing type field", {"index": i})
            continue

        # Add default name if missing
        if "name" not in server:
            server = server.copy()
            server["name"] = f"server_{i}"

        normalized.append(server)

    return normalized


async def apply_circuit_breaker_filtering(servers: List[Dict], circuit_breaker, backend_name: Optional[str] = None, agent_id: Optional[str] = None) -> List[Dict]:
    """Apply circuit breaker filtering to servers.

    Args:
        servers: List of server configurations
        circuit_breaker: Circuit breaker instance
        backend_name: Optional backend name for logging context
        agent_id: Optional agent ID for logging context

    Returns:
        List of servers that pass circuit breaker filtering
    """
    if not circuit_breaker:
        return servers

    filtered_servers = []
    for server in servers:
        server_name = server.get("name", "unnamed")
        if not await circuit_breaker.should_skip_server(server_name):
            filtered_servers.append(server)
        else:
            _log_mcp_activity(backend_name, "circuit breaker skipping server", {"server_name": server_name, "reason": "circuit_open"}, agent_id=agent_id)

    return filtered_servers
