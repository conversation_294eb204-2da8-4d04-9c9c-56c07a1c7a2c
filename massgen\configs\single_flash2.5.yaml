# Example Gemini configuration for MassGen 
# Usage: python -m massgen.cli --config example_gemini_config.yaml "Your question here"

# Single agent configuration
agent:
  id: "gemini_agent"
  # system_message: "You are a helpful AI assistant powered by Google Gemini."
  backend:
    type: "gemini"
    model: "gemini-2.5-flash"
    enable_web_search: true
    # enable_code_execution: true

# Alternative: Multi-agent configuration
# agents:
#   research_agent:
#     system_message: "You are a research specialist. Use web search for current information."
#     backend:
#       type: "gemini"
#       model: "gemini-2.5-flash"
#       enable_web_search: true
#       enable_code_execution: false
#   
#   compute_agent:
#     system_message: "You are a computational specialist. Use code execution for calculations."
#     backend:
#       type: "gemini"  
#       model: "gemini-2.5-flash"
#       enable_web_search: false
#       enable_code_execution: true
#   
#   analyst_agent:
#     system_message: "You are a general analyst providing clear reasoning."
#     backend:
#       type: "gemini"
#       model: "gemini-2.5-flash"
#       enable_web_search: false
#       enable_code_execution: false

# Display configuration
display:
  type: "rich_terminal"
  logging_enabled: true
